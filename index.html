<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>manou azadi - Portfolio & Design Blog</title>
    <meta name="description" content="Half portfolio, half blog. This page focuses on providing in english &amp; persian, interesting design discourses and code/design courses that aim to be complementary. As well, Typography &amp; Persian Calligraphy related content. Lastly, insight into the Apps &amp; Games, I develop. Enjoy. • <EMAIL> • +49 173 2995766">
    <meta name="keywords" content="english,persian,generative code,generative art,biomorphic art,islamic geometry,Code Tutorial,JavaScript,Kotlin,Swift,Python,typography,calligraphy,branding,design,corporate identity ,personal identity,package,apps,game,development">

    <!-- SEO Meta Tags -->
    <meta name="author" content="manou azadi">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <link rel="canonical" href="https://manouazadi.com/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://manouazadi.com/">
    <meta property="og:title" content="manou azadi - Portfolio & Design Blog">
    <meta property="og:description" content="Half portfolio, half blog focusing on design discourses, code tutorials, typography, Persian calligraphy, and app development insights.">
    <meta property="og:image" content="https://manouazadi.com/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="manou azadi">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://manouazadi.com/">
    <meta property="twitter:title" content="manou azadi - Portfolio & Design Blog">
    <meta property="twitter:description" content="Half portfolio, half blog focusing on design discourses, code tutorials, typography, Persian calligraphy, and app development insights.">
    <meta property="twitter:image" content="https://manouazadi.com/og-image.jpg">
    <meta property="twitter:creator" content="@manouazadi">

    <!-- Additional SEO -->
    <meta name="theme-color" content="#1a1a1a">
    <meta name="msapplication-TileColor" content="#1a1a1a">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "manou azadi",
        "url": "https://manouazadi.com",
        "email": "<EMAIL>",
        "telephone": "+49 173 2995766",
        "jobTitle": "Designer & Developer",
        "description": "Designer and developer specializing in generative art, typography, Persian calligraphy, and app development",
        "knowsAbout": ["Generative Art", "Typography", "Persian Calligraphy", "JavaScript", "Kotlin", "Swift", "Python", "Design", "Branding"],
        "sameAs": [
            "https://github.com/manouazadi",
            "https://linkedin.com/in/manouazadi",
            "https://twitter.com/manouazadi"
        ]
    }
    </script>

    <!-- Analytics -->
    <script defer src="https://cloud.umami.is/script.js" data-website-id="20ef9cb5-dd10-4949-a656-0c7eeeee1e5e"></script>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- GSAP Library & Plugins -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/Flip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/ScrollTrigger.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/ScrollSmoother.min.js"></script>
    <style>

        :root {
            --daytime: #ededed;
            --nighttime: #222222;

            --post-paragraph: #2c2c2c;
            --post-title: #151515;

            --ui-blur: 20px
           /*   background-color: var(--white); 
                border: 1px solid var(--blue);
                
           */
        }       

        html, body {
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 0;
            overflow: hidden; /* Prevents all scrolling on the body */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        /* Hide scrollbars for webkit browsers */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar {
            display: none;
        }

           body {
            /* TODO: add variables */
            font-family: 'Inter', sans-serif;
            background-color: var(--daytime);
            /* color: #414141; */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* TODO: change fonts */
        .header-font {
            font-family: 'Lora', serif;
        }

        /* Main content wrapper - full browser window without margins */
        #content-wrapper {
            margin: 0;
            height: 100vh; /* Full viewport height */
            width: 100vw; /* Full viewport width */
            position: relative;
        }

        /* Horizontal filter bar under the grid */
        #filter-buttons {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            display: flex;
            flex-direction: row;
            gap: 0.5rem;
        }

        /* Main container for horizontal scrolling */
        .scroll-container {
            width: 100%;
            height: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 2rem; /* Reduced padding for full window layout */
        }
        
        /* Custom scrollbar styles */
        .scroll-container::-webkit-scrollbar {
            height: 8px;
        }
        .scroll-container::-webkit-scrollbar-track {
            background: #000000;
            border-radius: 4px;
        }
        .scroll-container::-webkit-scrollbar-thumb {
            background: #ffffff;
            border-radius: 4px;
        }
        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: #e0e0e0;
        }

        /* The grid uses a neat, even layout */
        #bento-grid {
            display: grid;
            grid-template-rows: repeat(2, 200px);
            grid-auto-flow: column;
            grid-auto-columns: 200px;
            gap: 10px;
            height: 100%;
            width: max-content;
            align-content: center;
            padding: 0;
            /* Scale removed for full-size display */
        }

        /* Tile size classes */
        .tile-small {
            width: 200px;
            height: 200px;
            grid-column: span 1;
            grid-row: span 1;
            box-shadow: 0 2px 10px rgba(0,0,0,0.5);
        }

        .tile-wide {
            width: 400px;
            height: 200px;
            grid-column: span 2;
            grid-row: span 1;
            box-shadow: 0 2px 10px rgba(0,0,0,0.5);
        }

        .tile-wide.with-small-above {
            grid-row-start: 2;
        }

        .tile-wide.with-small-below {
            grid-row-start: 1;
        }

        /* .tile-small.above-wide and .tile-small.below-wide positions are set dynamically by JavaScript */

        .tile-large {
            width: 400px;
            height: 420px;
            grid-column: span 2;
            grid-row: span 2;
            box-shadow: 0 2px 10px rgba(0,0,0,0.5);
        }

        .tile-large.with-companion-above {
            grid-row-start: 2;
        }

        .tile-large.with-companion-below {
            grid-row-start: 1;
        }

        .tile-wide.above-large {
            grid-row-start: 1;
        }

        .tile-wide.below-large {
            grid-row-start: 3;
            transform: translateY(10px);
        }

        .tile-small.above-large {
            grid-row-start: 1;
        }

        .tile-small.below-large {
            grid-row-start: 3;
            transform: translateY(10px);
        }

        .grid-item {
            visibility: hidden; /* Initial state for load-in animation */
            background-color: #1a1a1a;
            border-radius: 0.75rem; /* 12px */
            overflow: hidden;
            position: relative;
            transform-style: preserve-3d;
            transform: perspective(1000px);
            cursor: pointer;
        }
        
        .grid-item[data-hidden="true"] {
            display: none;
        }

        /* Parallax Layer Styling */
        .parallax-layer {
            position: absolute;
            top: -5%; /* Position outside the frame to hide edges during movement */
            left: -5%;
            width: 110%;
            height: 110%;
            background-size: cover;
            background-position: center;
        }

        .layer-title {
            z-index: 6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.5);
        }

        .layer-fg { z-index: 5; }
        .layer-mg { z-index: 4; }
        .layer-mg-2 { z-index: 3; }
        .layer-bg { z-index: 2; }
        .layer-bg-2 { z-index: 1; }
        
        .filter-btn {
            background-color: transparent;
            border: none;
            padding: 0;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-btn::before {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .filter-btn.active::before {
            border-color: rgba(255, 255, 255, 0.8);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }

        .filter-btn:hover::before {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        }

        /* Category colors 
        TODO: make much simpler
        */
        .filter-btn[data-filter="all"]::before {
            background-color: #5db6ff;
            /* background: linear-gradient(45deg, #ff6347, #20b2aa, #228b22, #4682b4, #9932cc); */
        }

        .filter-btn[data-filter="art"]::before {
            background-color: #e3ad0b;
        }

        .filter-btn[data-filter="architecture"]::before {
            background-color: #365772;
        }

        .filter-btn[data-filter="nature"]::before {
            background-color: #0d9021;
        }

        .filter-btn[data-filter="tech"]::before {
            background-color: #4682b4;
        }

        /* Mobile Adjustments */
        /* @media (max-width: 768px) {
            #content-wrapper {
                margin: 15px;
                height: calc(100vh - 10px);
                width: calc(100vw - 10px);
            }

            #filter-buttons {
                bottom: 15px;
                gap: 0.4rem;
            }

            .filter-btn {
                width: 24px;
                height: 24px;
            }

            .filter-btn::before {
                width: 14px;
                height: 14px;
            }

            .tile-small {
                width: 150px;
                height: 150px;
            }

            .tile-wide {
                width: 300px;
                height: 150px;
            }

            .tile-large {
                width: 300px;
                height: 300px;
            }

            .layer-title {
                font-size: 1.75rem;
            }
        } */
        
        /* Permission Modal */
        #permission-modal {
            background-color: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
        }
        
        /* Gradient Fade */
        #left-gradient {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 100px;
            background: linear-gradient(to right, #0a0a0a, transparent);
            z-index: 5;
            pointer-events: none;
            opacity: 0;
        }

        /* Fullscreen Modal Styles */
        #fullscreen-modal {
            position: fixed;
            inset: 20px; /* 20px margin */

            /* TODO: add tune z-index later */
            z-index: 9999;
            visibility: hidden; /* Initially hidden */
            transform-style: preserve-3d;
            perspective: 1000px;
            opacity: 0;
        }

        #fullscreen-content {
            width: 100%;
            height: 100%;
            
            /* TODO: add variables / tune blur */
            background-color: #ffffffc6;
            backdrop-filter: blur (var(--ui-blur));

            border-radius: 0.75rem;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
            padding: 2rem;
            display: flex;
            align-items: center;
        }

        .fullscreen-detail-content {
            min-width: 100%;
            width: max-content;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0;
        }

        .fullscreen-detail-content h2 {
            white-space: nowrap;
            margin-bottom: 2rem;
            align-self: flex-start;
        }

        .fullscreen-content-body {
            display: flex;
            gap: 3rem;
            align-items: flex-start;
            width: 100%;
        }

        .fullscreen-left-panel {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            flex-shrink: 0;
        }

        .fullscreen-image {
            width: 200px;
            height: 200px;
            border-radius: 0.5rem;
            object-fit: cover;
        }

        .table-of-contents {
            width: 200px;
            background-color: rgba(255, 255, 255, 0.08);
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid #333;
        }

        .table-of-contents h4 {
            color: #fff;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            text-align: center;
            border-bottom: 1px solid #444;
            padding-bottom: 0.5rem;
        }

        .toc-item {
            display: block;
            color: #bbb;
            text-decoration: none;
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .toc-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            transform: translateX(4px);
        }

         .toc-item.active {
            /* TODO: add variables */
            background-color: #3838389b;
            backdrop-filter: blur(5px);
            box-shadow:rgba(0, 0, 0, 0.1) 0 0 30px;
            color: #fff;
        }

        .fullscreen-text {
            flex: 1;
            max-width: none;
            overflow-x: auto;
            overflow-y: hidden;
            display: flex;
            gap: 3rem;
            padding-bottom: 1rem;
        }

        .fullscreen-text p {
            white-space: normal;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .fullscreen-section {
            min-width: 350px;
            max-width: 400px;
            flex-shrink: 0;
            padding: 1.5rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 0.75rem;
            border: 1px solid #333;
        }

        .fullscreen-section:last-child {
            margin-right: 2rem;
        }

        .fullscreen-section h3 {
            color: #fff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            font-family: 'Lora', serif;
            border-bottom: 2px solid #ff6347;
            padding-bottom: 0.5rem;
        }

        .fullscreen-section h4 {
            color: #ccc;
            font-size: 1.25rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
        }

        .fullscreen-section ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .fullscreen-section li {
            color: #bbb;
            line-height: 1.5;
            margin-bottom: 0.5rem;
        }

        .fullscreen-section p {
            color: var(--post-paragraph);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        /* Enhanced scrollbars - black and white */

        /* Main horizontal scrollbar */
        /* .scroll-container::-webkit-scrollbar {
            height: 8px;
        }

        .scroll-container::-webkit-scrollbar-track {
            background: #000000;
            border-radius: 4px;
            margin: 0 5px;
            position: relative;
        }

        .scroll-container::-webkit-scrollbar-thumb {
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            min-width: 40px;
            position: relative;
        }

        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: #e0e0e0;
            border: 1px solid rgba(0, 0, 0, 0.3);
            transform: scaleY(1.2);
        } */

        /* Fullscreen modal horizontal scrollbar */
        .fullscreen-text::-webkit-scrollbar {
            height: 6px;
        }

        .fullscreen-text::-webkit-scrollbar-track {
            background: #000000;
            border-radius: 3px;
            margin: 0 3px;
        }

        .fullscreen-text::-webkit-scrollbar-thumb {
            background: #ffffff;
            border-radius: 3px;
            min-width: 30px;
        }

        .fullscreen-text::-webkit-scrollbar-thumb:hover {
            background: #e0e0e0;
            transform: scaleY(1.3);
        }

        /* Fullscreen modal content scrollbar */
        #fullscreen-content::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        #fullscreen-content::-webkit-scrollbar-track {
            background: #000000;
            border-radius: 3px;
        }

        #fullscreen-content::-webkit-scrollbar-thumb {
            background: #ffffff;
            border-radius: 3px;
            min-height: 30px;
            min-width: 30px;
        }

        #fullscreen-content::-webkit-scrollbar-thumb:hover {
            background: #e0e0e0;
            transform: scale(1.2);
        }

        /* Progress is now integrated into scrollbar handles */

        /* Scrollbar corner styling */
        ::-webkit-scrollbar-corner {
            background: #000000;
        }

        /* Enhanced scrollbar track animations */
        .scroll-container::-webkit-scrollbar-track:hover {
            background: #1a1a1a;
        }

        .fullscreen-text::-webkit-scrollbar-track:hover {
            background: #1a1a1a;
        }

        #fullscreen-content::-webkit-scrollbar-track:hover {
            background: #1a1a1a;
        }

        /* Mobile scrollbar adjustments */
        @media (max-width: 768px) {
            .scroll-container::-webkit-scrollbar {
                height: 6px;
            }

            .scroll-container::-webkit-scrollbar-thumb {
                min-width: 30px;
                background: #ffffff;
            }

            .fullscreen-text::-webkit-scrollbar {
                height: 4px;
            }

            .fullscreen-text::-webkit-scrollbar-thumb {
                min-width: 20px;
                background: #ffffff;
            }

            #fullscreen-content::-webkit-scrollbar {
                width: 4px;
                height: 4px;
            }

            #fullscreen-content::-webkit-scrollbar-thumb {
                min-height: 20px;
                min-width: 20px;
                background: #ffffff;
            }
        }

        #fullscreen-close-btn {
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 2.5rem;
            color: white;
            cursor: pointer;
            z-index: 110;
            opacity: 0;
            pointer-events: none;
            text-shadow: 0 0 10px black;
        }

        /* Loading Screen Styles */
        #loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.8s ease-out;
        }

        #loading-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 2rem;
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
        }

        .loading-progress {
            margin-top: 1rem;
            text-align: center;
        }

        .loading-percentage {
            color: #e4e4e4;
            font-family: 'Lora', serif;
            font-size: 0.875rem;
            font-weight: 300;
            letter-spacing: 1px;
            opacity: 0.6;
        }

        .loading-text {
            color: #e4e4e4;
            font-family: 'Lora', serif;
            font-size: 1.5rem;
            font-weight: 400;
            letter-spacing: 2px;
            opacity: 0.8;
        }

        /* SVG stroke animation */
        .loading-logo path,
        .loading-logo circle,
        .loading-logo rect,
        .loading-logo polygon {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            transition: stroke-dashoffset 0.1s ease-out;
        }

        /* Subtle glow animation */
        @keyframes logoGlow {
            0%, 100% {
                filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
            }
            50% {
                filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.5));
            }
        }

        .loading-logo.animate-glow {
            animation: logoGlow 2s ease-in-out infinite;
        }

        /* Left Sidebar Menu */
        #left-sidebar {
            position: fixed;
            left: 0;
            top: 20px;
            bottom: 20px;
            height: calc(100vh - 40px); /* Full height minus 20px top and bottom margins */
            width: 60px;
            z-index: 9990;
            pointer-events: none;
        }

        .sidebar-lines {
            position: absolute;
            left: 20px;
            top: 0;
            height: 100%;
            width: 20px;
            pointer-events: none;
        }

        .sidebar-line {
            position: absolute;
            width: 1px;
            height: 100%;
            background: #666;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-line.left {
            left: 0;
        }

        .sidebar-line.right {
            right: 0;
        }

        .sidebar-line.right.expanded {
            transform: translateX(300px);
        }

        .sidebar-trigger {
            position: absolute;
            left: 20px;
            top: 0;
            width: 20px;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: all;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-trigger:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-trigger::after {
            content: '→';
            color: white;
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .sidebar-menu {
            position: absolute;
            left: -300px;
            top: 0;
            width: 300px;
            height: 100%;
            background: rgba(26, 26, 26, 0.75);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: all;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            z-index: 999;
            justify-content: space-evenly;
        }

        .sidebar-menu.expanded {
            left: 40px;
        }

        .sidebar-trigger.expanded::after {
            transform: rotate(180deg);
        }

        .menu-section {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #ccc;
            font-family: 'Lora', serif;
            font-size: 0.95rem;
            font-weight: 400;
            letter-spacing: 1px;
            border-radius: 6px;
        }

        .menu-section:hover {
            color: white;
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(0.25rem);
        }

        .menu-section.active {
            color: #ff6347;
            font-weight: 500;
            background: rgba(255, 99, 71, 0.1);
        }

        /* Style for blurring the background content */
        #content-wrapper.blurred {
            filter: blur(10px);
            /* Removed zoom out effect - transform: scale(0.98); */
            transition: all 0.5s ease-out;
        }
        
        /* Admin UI Styles */
        .admin-toggle-btn {
            display: none; /* Hidden by default */
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            z-index: 20;
            padding: 0.5rem;
            background-color: rgba(0,0,0,0.5);
            border-radius: 9999px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .admin-toggle-btn:hover {
            background-color: rgba(0,0,0,0.8);
        }
        #content-wrapper.admin-mode .admin-toggle-btn {
            display: block; /* Show when in admin mode */
        }
        .grid-item[data-visible="false"] .visible-icon {
            display: none;
        }
        .grid-item[data-visible="true"] .hidden-icon {
            display: none;
        }

        /* TODO: Fix*/
        .text-4xl {
            position: absolute;
            top: 165px;
        }


    </style>
</head>
<body class="antialiased" data-index="true">

    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-logo-container">
            <svg id="loading-logo" class="loading-logo" xmlns="http://www.w3.org/2000/svg">
                <!-- Logo will be loaded dynamically -->
            </svg>
            <div class="loading-progress">
                <div class="loading-percentage">0%</div>
            </div>
        </div>

        <div class="loading-text">manou azadi</div>
    </div>

    <!-- Left Sidebar Menu -->
    <div id="left-sidebar">
        <div class="sidebar-lines">
            <div class="sidebar-line left"></div>
            <div class="sidebar-line right"></div>
        </div>

        <div class="sidebar-trigger" id="sidebar-trigger"></div>

        <div class="sidebar-menu" id="sidebar-menu">
            <div class="menu-section active" data-section="home">Home</div>
            <div class="menu-section" data-section="projects">Projects</div>
            <div class="menu-section" data-section="archive">Archive</div>
            <div class="menu-section" data-section="about">About</div>
        </div>
    </div>

    <!-- Permission Modal for Gyroscope -->
    <div id="permission-modal" class="fixed inset-0 z-50 flex-col items-center justify-center hidden">
        <div class="text-center text-white p-8">
            <h2 class="text-2xl font-bold mb-4">Motion-Based Effects</h2>
            <p class="mb-6">Please grant permission to use your device's motion sensors for a more immersive experience.</p>
            <button id="permission-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full">Enable Motion</button>
        </div>
    </div>

    <!-- Fullscreen Modal Container -->
    <div id="fullscreen-modal">
        <div id="fullscreen-content"></div>
        <div id="fullscreen-close-btn">&times;</div>
    </div>


    <!-- Main Content -->
    <main id="content-wrapper" class="transition-all duration-500" role="main">

        <!-- Gradient Overlay -->
        <div id="left-gradient"></div>

        <!-- Navigation / Filter Buttons -->
        <nav id="filter-buttons" role="navigation" aria-label="Content category filters">
            <button class="filter-btn active" data-filter="all" title="All Categories" aria-label="Show all categories"></button>
            <button class="filter-btn" data-filter="art" title="Art" aria-label="Show art category"></button>
            <button class="filter-btn" data-filter="architecture" title="Architecture" aria-label="Show architecture category"></button>
            <button class="filter-btn" data-filter="nature" title="Nature" aria-label="Show nature category"></button>
            <button class="filter-btn" data-filter="tech" title="Tech" aria-label="Show tech category"></button>
        </nav>

        <!-- Horizontal Scroll Container -->
        <div class="scroll-container">
            <!-- Portfolio Grid -->
            <section id="bento-grid" role="region" aria-label="Portfolio and blog content grid">
                
                 <!-- todo: Art - Large tile -- extra layers -->
                <div class="grid-item tile-large" data-category="art" data-visible="true" data-date="2024-07-05" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Generative Art • 1</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('../vite2/src/images/posts/large/GA1/GA_L2.png')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('../vite2/src/images/posts/large/GA1/GA_L3.png')"></div>
                    <div class="parallax-layer layer-mg-2" style="background-image: url('..')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('..')"></div>
                    <div class="parallax-layer layer-bg-2" style="background-image: url('../vite2/src/images/posts/large/GA1/GA_L4.png')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Essence of Art</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/ff6347/ffffff?text=Art" alt="Art" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="overview">Overview</a>
                                    <a class="toc-item" data-target="art-forms">Art Forms</a>
                                    <a class="toc-item" data-target="historical">Historical Significance</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="overview">
                                    <h3>Overview</h3>
                                    <p class="text-lg text-gray-300">Art is a diverse range of human activity, and resulting product, that involves creative or imaginative talent expressive of technical proficiency, beauty, emotional power, or conceptual ideas.</p>
                                    <p class="text-lg text-gray-300">There is no generally agreed definition of what constitutes art, and its interpretation has varied greatly throughout history and in different cultures.</p>
                                </div>

                                <div class="fullscreen-section" id="art-forms">
                                    <h3>Art Forms</h3>
                                    <h4>Visual Arts</h4>
                                    <ul>
                                        <li>Painting - Expression through pigments and color</li>
                                        <li>Sculpture - Three-dimensional artistic creation</li>
                                        <li>Drawing - Linear artistic expression</li>
                                        <li>Photography - Capturing moments in time</li>
                                    </ul>
                                    <h4>Performing Arts</h4>
                                    <ul>
                                        <li>Theater - Live dramatic performance</li>
                                        <li>Dance - Movement as artistic expression</li>
                                        <li>Music - Auditory artistic creation</li>
                                    </ul>
                                </div>

                                <div class="fullscreen-section" id="historical">
                                    <h3>Historical Significance</h3>
                                    <p class="text-lg text-gray-300">Art has been a fundamental part of human culture for thousands of years, serving as a means of communication, expression, and cultural preservation.</p>
                                    <p class="text-lg text-gray-300">From cave paintings to digital art, artistic expression continues to evolve with technology and society.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Architecture - Large tile -->
                <div class="grid-item tile-large" data-category="architecture" data-visible="true" data-date="2024-07-04" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Arch</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/20b2aa/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/696969/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/708090/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Principles of Architecture</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/20b2aa/ffffff?text=Architecture" alt="Architecture" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="definition">Definition</a>
                                    <a class="toc-item" data-target="principles">Core Principles</a>
                                    <a class="toc-item" data-target="styles">Architectural Styles</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="definition">
                                    <h3>Definition</h3>
                                    <p class="text-lg text-gray-300">Architecture is both the process and the product of planning, designing, and constructing buildings or other structures. Architectural works, in the material form of buildings, are often perceived as cultural symbols and as works of art.</p>
                                </div>

                                <div class="fullscreen-section" id="principles">
                                    <h3>Core Principles</h3>
                                    <ul>
                                        <li>Functionality - Buildings must serve their intended purpose</li>
                                        <li>Structural Integrity - Safe and stable construction</li>
                                        <li>Aesthetic Appeal - Visual harmony and beauty</li>
                                        <li>Sustainability - Environmental responsibility</li>
                                        <li>Context - Harmony with surroundings</li>
                                    </ul>
                                </div>

                                <div class="fullscreen-section" id="styles">
                                    <h3>Architectural Styles</h3>
                                    <h4>Classical</h4>
                                    <p class="text-lg text-gray-300">Based on ancient Greek and Roman principles, emphasizing symmetry, proportion, and order.</p>
                                    <h4>Modern</h4>
                                    <p class="text-lg text-gray-300">Characterized by clean lines, minimal ornamentation, and emphasis on function.</p>
                                    <h4>Contemporary</h4>
                                    <p class="text-lg text-gray-300">Current architectural trends incorporating new materials and technologies.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Nature - Small tile -->
                <div class="grid-item tile-small" data-category="nature" data-visible="true" data-date="2024-07-03" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Nature</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/228b22/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/006994/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/8b4513/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Beauty of Nature</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/228b22/ffffff?text=Nature" alt="Nature" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <div class="fullscreen-section">
                                    <h3>What is Nature?</h3>
                                    <p class="text-lg text-gray-300">Nature, in the broadest sense, is the physical world or universe. "Nature" can refer to the phenomena of the physical world, and also to life in general. The study of nature is a large, if not the only, part of science.</p>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Natural Ecosystems</h3>
                                    <ul>
                                        <li>Forests - Complex woodland environments</li>
                                        <li>Oceans - Marine ecosystems covering 71% of Earth</li>
                                        <li>Mountains - High-altitude environments</li>
                                        <li>Deserts - Arid landscapes with unique adaptations</li>
                                        <li>Wetlands - Crucial biodiversity hotspots</li>
                                    </ul>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Conservation</h3>
                                    <p class="text-lg text-gray-300">Protecting natural environments is crucial for maintaining biodiversity and ensuring sustainable futures for all life on Earth.</p>
                                    <p class="text-lg text-gray-300">Climate change and human activity pose significant challenges to natural ecosystems worldwide.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tech - Small tile -->
                <div class="grid-item tile-small" data-category="tech" data-visible="true" data-date="2024-07-02" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Tech</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/00ced1/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/000080/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The World of Tech</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/4682b4/ffffff?text=Tech" alt="Technology" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <div class="fullscreen-section">
                                    <h3>Technology Overview</h3>
                                    <p class="text-lg text-gray-300">Technology is the sum of any techniques, skills, methods, and processes used in the production of goods or services or in the accomplishment of objectives, such as scientific investigation.</p>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Key Technology Areas</h3>
                                    <h4>Artificial Intelligence</h4>
                                    <ul>
                                        <li>Machine Learning - Systems that learn from data</li>
                                        <li>Natural Language Processing - Understanding human language</li>
                                        <li>Computer Vision - Interpreting visual information</li>
                                    </ul>
                                    <h4>Web Technologies</h4>
                                    <ul>
                                        <li>Frontend Development - User interface creation</li>
                                        <li>Backend Systems - Server-side logic and databases</li>
                                        <li>Cloud Computing - Distributed computing resources</li>
                                    </ul>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Future Trends</h3>
                                    <p class="text-lg text-gray-300">Emerging technologies like quantum computing, blockchain, and IoT are reshaping how we interact with the digital world.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Sculpture - Wide tile -->
                <div class="grid-item tile-wide" data-category="art" data-visible="true" data-date="2024-07-01" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Sculpture</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x400/daa520/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x400/ff6347/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x400/dc143c/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Forms in Sculpture</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/daa520/ffffff?text=Sculpture" alt="Sculpture" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <div class="fullscreen-section">
                                    <h3>About Sculpture</h3>
                                    <p class="text-lg text-gray-300">Sculpture is the branch of the visual arts that operates in three dimensions. It is one of the plastic arts. Durable sculptural processes originally used carving and modelling, in stone, metal, ceramics, wood and other materials.</p>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Sculptural Techniques</h3>
                                    <h4>Subtractive Methods</h4>
                                    <ul>
                                        <li>Carving - Removing material to reveal form</li>
                                        <li>Stone cutting - Traditional masonry techniques</li>
                                        <li>Wood carving - Working with natural grain</li>
                                    </ul>
                                    <h4>Additive Methods</h4>
                                    <ul>
                                        <li>Modeling - Building up with clay or wax</li>
                                        <li>Casting - Creating from molds</li>
                                        <li>Welding - Joining metal components</li>
                                    </ul>
                                </div>

                                <div class="fullscreen-section">
                                    <h3>Materials</h3>
                                    <p class="text-lg text-gray-300">From traditional marble and bronze to contemporary plastics and digital materials, sculptors continue to explore new mediums for artistic expression.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ocean - Small tile -->
                 <div class="grid-item tile-small" data-category="nature" data-visible="true" data-date="2024-06-30" data-index="true">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Ocean</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/006994/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Deep Ocean</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/006994/ffffff?text=Ocean" alt="Ocean" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">The ocean is a body of water that composes much of a planet's hydrosphere. On Earth, an ocean is one of the major conventional divisions of the World Ocean.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Brutalism - Small tile -->
                <div class="grid-item tile-small" data-category="architecture" data-visible="true" data-date="2024-06-29" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Brutalism</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/696969/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/708090/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Brutalist Architecture</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/696969/ffffff?text=Brutalism" alt="Brutalism" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Brutalist architecture is a style that emerged in the 1950s and grew out of the early-20th century modernist movement. Brutalist buildings are characterised by their massive, monolithic and 'blocky' appearance with a rigid geometric style and large-scale use of poured concrete.</p>
                            </div>
                        </div>
                    </div>
                </div>
                 <!-- Data - Small tile -->
                 <div class="grid-item tile-small" data-category="tech" data-visible="true" data-date="2024-06-28" data-index="false">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Data</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/800080/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/00ced1/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Power of Data</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/800080/ffffff?text=Data" alt="Data" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Data are individual facts, statistics, or items of information, often numeric. In a more technical sense, data are a set of values of qualitative or quantitative variables about one or more persons or objects.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Forest - Large tile -->
                <div class="grid-item tile-large" data-category="nature" data-visible="true" data-date="2024-06-27" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Forest</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x600/228b22/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x600/8b4513/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x600/006994/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Into the Forest</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/228b22/ffffff?text=Forest" alt="Forest" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">A forest is an area of land dominated by trees. Hundreds of definitions of forest are used throughout the world, incorporating factors such as tree density, tree height, land use, legal standing and ecological function.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Painting - Large tile -->
                 <div class="grid-item tile-large" data-category="art" data-visible="true" data-date="2024-06-26" data-index="true">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Painting</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/dc143c/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/ff6347/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/daa520/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Art of Painting</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/dc143c/ffffff?text=Painting" alt="Painting" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Painting is the practice of applying paint, pigment, color or other medium to a solid surface. The medium is commonly applied to the base with a brush, but other implements, such as knives, sponges, and airbrushes, can be used.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Code - Small tile -->
                <div class="grid-item tile-small" data-category="tech" data-visible="true" data-date="2024-06-25" data-index="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Code</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/000080/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/4682b4/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/00ced1/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Language of Code</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/000080/ffffff?text=Code" alt="Code" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">In computing, source code is any collection of code, with or without comments, written using a human-readable programming language, usually as plain text. The source code of a program is specially designed to facilitate the work of computer programmers.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Music - Large tile -->
                <div class="grid-item tile-large" data-category="art" data-visible="true" data-date="2024-06-24" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Music</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/9932cc/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/ff6347/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/daa520/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Art of Music</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/9932cc/ffffff?text=Music" alt="Music" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="overview">Overview</a>
                                    <a class="toc-item" data-target="elements">Musical Elements</a>
                                    <a class="toc-item" data-target="genres">Genres</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="overview">
                                    <h3>Overview</h3>
                                    <p class="text-lg text-gray-300">Music is an art form and cultural activity whose medium is sound organized in time. General definitions of music include common elements such as pitch, rhythm, dynamics, and the sonic qualities of timbre and texture.</p>
                                </div>
                                <div class="fullscreen-section" id="elements">
                                    <h3>Musical Elements</h3>
                                    <ul>
                                        <li>Rhythm - The timing of musical sounds</li>
                                        <li>Melody - A sequence of musical tones</li>
                                        <li>Harmony - The combination of simultaneous tones</li>
                                        <li>Dynamics - The volume of musical sounds</li>
                                    </ul>
                                </div>
                                <div class="fullscreen-section" id="genres">
                                    <h3>Genres</h3>
                                    <p class="text-lg text-gray-300">Music spans countless genres from classical and jazz to rock, electronic, and world music, each with its own characteristics and cultural significance.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Photography - Wide tile -->
                <div class="grid-item tile-wide" data-category="art" data-visible="true" data-date="2024-06-23" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Photo</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/ff8c00/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/daa520/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/ff6347/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Photography</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/ff8c00/ffffff?text=Photo" alt="Photography" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="definition">Definition</a>
                                    <a class="toc-item" data-target="techniques">Techniques</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="definition">
                                    <h3>Definition</h3>
                                    <p class="text-lg text-gray-300">Photography is the art, application, and practice of creating durable images by recording light or other electromagnetic radiation, either electronically by means of an image sensor, or chemically by means of a light-sensitive material.</p>
                                </div>
                                <div class="fullscreen-section" id="techniques">
                                    <h3>Techniques</h3>
                                    <p class="text-lg text-gray-300">Photography encompasses various techniques including composition, lighting, exposure control, and post-processing to create compelling visual narratives.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI - Small tile -->
                <div class="grid-item tile-small" data-category="tech" data-visible="true" data-date="2024-06-22" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">AI</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/00ced1/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/000080/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Artificial Intelligence</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/00ced1/ffffff?text=AI" alt="AI" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="overview">Overview</a>
                                    <a class="toc-item" data-target="applications">Applications</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="overview">
                                    <h3>Overview</h3>
                                    <p class="text-lg text-gray-300">Artificial intelligence is intelligence demonstrated by machines, in contrast to the natural intelligence displayed by humans and animals.</p>
                                </div>
                                <div class="fullscreen-section" id="applications">
                                    <h3>Applications</h3>
                                    <p class="text-lg text-gray-300">AI applications include machine learning, natural language processing, computer vision, robotics, and autonomous systems.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mountains - Small tile -->
                <div class="grid-item tile-small" data-category="nature" data-visible="true" data-date="2024-06-21" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Mountains</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/8b4513/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/228b22/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/006994/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Mountain Ranges</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/8b4513/ffffff?text=Mountains" alt="Mountains" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="formation">Formation</a>
                                    <a class="toc-item" data-target="ecosystems">Ecosystems</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="formation">
                                    <h3>Formation</h3>
                                    <p class="text-lg text-gray-300">Mountains are formed through tectonic forces, volcanism, or erosion. They are elevated portions of the Earth's crust with steep sides and significant exposed bedrock.</p>
                                </div>
                                <div class="fullscreen-section" id="ecosystems">
                                    <h3>Ecosystems</h3>
                                    <p class="text-lg text-gray-300">Mountain ecosystems are diverse and unique, hosting specialized flora and fauna adapted to high altitude conditions.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Design - Wide tile -->
                <div class="grid-item tile-wide" data-category="art" data-visible="true" data-date="2024-06-20" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Design</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/ff1493/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/ff6347/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/daa520/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Design Principles</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/ff1493/ffffff?text=Design" alt="Design" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="principles">Principles</a>
                                    <a class="toc-item" data-target="process">Process</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="principles">
                                    <h3>Principles</h3>
                                    <p class="text-lg text-gray-300">Design is the creation of a plan or convention for the construction of an object, system or measurable human interaction.</p>
                                </div>
                                <div class="fullscreen-section" id="process">
                                    <h3>Process</h3>
                                    <p class="text-lg text-gray-300">The design process involves research, ideation, prototyping, testing, and iteration to create effective solutions.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Space - Large tile -->
                <div class="grid-item tile-large" data-category="nature" data-visible="true" data-date="2024-06-19" data-index="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Space</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/191970/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/000080/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/4682b4/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Outer Space</h2>
                        <div class="fullscreen-content-body">
                            <div class="fullscreen-left-panel">
                                <img src="https://placehold.co/200x200/191970/ffffff?text=Space" alt="Space" class="fullscreen-image">
                                <div class="table-of-contents">
                                    <h4>Contents</h4>
                                    <a class="toc-item" data-target="overview">Overview</a>
                                    <a class="toc-item" data-target="exploration">Exploration</a>
                                    <a class="toc-item" data-target="future">Future</a>
                                </div>
                            </div>
                            <div class="fullscreen-text">
                                <div class="fullscreen-section" id="overview">
                                    <h3>Overview</h3>
                                    <p class="text-lg text-gray-300">Outer space is the expanse that exists beyond Earth and between celestial bodies. It contains low densities of particles, predominantly hydrogen plasma, electromagnetic radiation, magnetic fields, neutrinos, dust, and cosmic rays.</p>
                                </div>
                                <div class="fullscreen-section" id="exploration">
                                    <h3>Exploration</h3>
                                    <p class="text-lg text-gray-300">Space exploration has led to incredible discoveries about our universe, from the Moon landings to Mars rovers and deep space telescopes.</p>
                                </div>
                                <div class="fullscreen-section" id="future">
                                    <h3>Future</h3>
                                    <p class="text-lg text-gray-300">The future of space exploration includes missions to Mars, asteroid mining, and potentially interstellar travel.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // SEO: Handle indexing control based on data attributes
            handleIndexingControl();

            gsap.registerPlugin(Flip, ScrollTrigger);

            const contentWrapper = document.getElementById('content-wrapper');
            const grid = document.getElementById('bento-grid');
            const scrollContainer = document.querySelector('.scroll-container');
            const gridItems = gsap.utils.toArray('.grid-item');
            const filterButtonsContainer = document.getElementById('filter-buttons');
            const buttons = gsap.utils.toArray('.filter-btn');
            const permissionModal = document.getElementById('permission-modal');
            const permissionBtn = document.getElementById('permission-btn');
            const leftGradient = document.getElementById('left-gradient');
            const loadingScreen = document.getElementById('loading-screen');
            const sidebarTrigger = document.getElementById('sidebar-trigger');
            const sidebarMenu = document.getElementById('sidebar-menu');
            const rightLine = document.querySelector('.sidebar-line.right');

            // Loading Screen Logic
            let loadingProgress = 0;
            let loadingComplete = false;
            let logoLoaded = false;

            async function loadLogo() {
                try {
                    const response = await fetch('/public/logo.svg');
                    const svgText = await response.text();
                    const logoContainer = document.getElementById('loading-logo');
                    logoContainer.innerHTML = svgText;

                    // Get all stroke elements and prepare them for animation
                    const strokeElements = logoContainer.querySelectorAll('path, circle, rect, polygon, line');
                    strokeElements.forEach(element => {
                        // Calculate the total length of each path
                        const length = element.getTotalLength ? element.getTotalLength() : 1000;
                        element.style.strokeDasharray = length;
                        element.style.strokeDashoffset = length;
                        element.setAttribute('data-length', length);
                    });

                    logoLoaded = true;
                    logoContainer.classList.add('animate-glow');
                    updateLoadingProgress(10); // Logo loaded
                } catch (error) {
                    console.error('Failed to load logo:', error);
                    // Fallback to text-based loading
                    document.getElementById('loading-logo').innerHTML = '<div style="color: white; font-size: 2rem;">M</div>';
                    logoLoaded = true;
                    updateLoadingProgress(10);
                }
            }

            function updateLoadingProgress(progress) {
                loadingProgress = Math.min(progress, 100);
                const percentageElement = document.querySelector('.loading-percentage');
                if (percentageElement) {
                    percentageElement.textContent = `${Math.round(loadingProgress)}%`;
                }

                // Update stroke animation based on progress
                if (logoLoaded) {
                    const logoContainer = document.getElementById('loading-logo');
                    const strokeElements = logoContainer.querySelectorAll('path, circle, rect, polygon, line');

                    strokeElements.forEach(element => {
                        const length = parseFloat(element.getAttribute('data-length')) || 1000;
                        const offset = length - (length * (loadingProgress / 100));
                        element.style.strokeDashoffset = offset;
                    });
                }

                // Check if loading is complete
                if (loadingProgress >= 100 && !loadingComplete) {
                    loadingComplete = true;
                    setTimeout(hideLoadingScreen, 2000); // Show completed logo for 2 seconds
                }
            }

            function simulateLoading() {
                // Simulate various loading stages
                const stages = [
                    { progress: 20, delay: 200, label: 'Loading assets...' },
                    { progress: 40, delay: 300, label: 'Initializing...' },
                    { progress: 60, delay: 400, label: 'Preparing content...' },
                    { progress: 80, delay: 300, label: 'Almost ready...' },
                    { progress: 100, delay: 200, label: 'Complete!' }
                ];

                let currentStage = 0;
                function nextStage() {
                    if (currentStage < stages.length) {
                        const stage = stages[currentStage];
                        updateLoadingProgress(stage.progress);
                        currentStage++;
                        setTimeout(nextStage, stage.delay);
                    }
                }
                nextStage();
            }

            function hideLoadingScreen() {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 800); // Match the CSS transition duration
            }

            // Initialize loading
            loadLogo();
            setTimeout(simulateLoading, 500); // Start simulation after logo loads
            
            // Fullscreen Modal elements
            const fullscreenModal = document.getElementById('fullscreen-modal');
            const fullscreenContent = document.getElementById('fullscreen-content');
            const fullscreenCloseBtn = document.getElementById('fullscreen-close-btn');
            let activeItem = null;
            let isAnimating = false;
            let isAdminMode = false;

            // --- SEO Indexing Control ---
            function handleIndexingControl() {
                const body = document.body;
                const shouldIndex = body.dataset.index !== 'false';

                // If data-index="false", add noindex meta tag
                if (!shouldIndex) {
                    const noIndexMeta = document.createElement('meta');
                    noIndexMeta.name = 'robots';
                    noIndexMeta.content = 'noindex, nofollow';
                    document.head.appendChild(noIndexMeta);

                    // Also update existing robots meta tag if present
                    const existingRobotsMeta = document.querySelector('meta[name="robots"]');
                    if (existingRobotsMeta) {
                        existingRobotsMeta.content = 'noindex, nofollow';
                    }

                    console.log('SEO: Page set to noindex due to data-index="false"');
                } else {
                    console.log('SEO: Page is indexable');
                }

                // Handle individual content items
                const gridItems = document.querySelectorAll('.grid-item');
                gridItems.forEach(item => {
                    if (item.dataset.index === 'false') {
                        item.setAttribute('data-noindex', 'true');
                        // Add structured data to exclude from search results
                        item.style.setProperty('--seo-exclude', 'true');
                    }
                });
            }

            // --- SEO Enhancements ---
            function generateSitemapData() {
                const indexableItems = Array.from(document.querySelectorAll('.grid-item'))
                    .filter(item => item.dataset.index !== 'false')
                    .map(item => {
                        const title = item.querySelector('.layer-title')?.textContent || 'Untitled';
                        const category = item.dataset.category || 'uncategorized';
                        const date = item.dataset.date || new Date().toISOString().split('T')[0];

                        return {
                            title,
                            category,
                            date,
                            url: `#${title.toLowerCase().replace(/\s+/g, '-')}`,
                            lastmod: date
                        };
                    });

                console.log('SEO: Sitemap data generated for', indexableItems.length, 'items');
                return indexableItems;
            }

            // Add structured data for better SEO
            function addStructuredData() {
                const portfolioData = {
                    "@context": "https://schema.org",
                    "@type": "CreativeWork",
                    "name": "manou azadi Portfolio",
                    "description": "Portfolio and blog featuring design discourses, code tutorials, typography, and Persian calligraphy",
                    "author": {
                        "@type": "Person",
                        "name": "manou azadi",
                        "email": "<EMAIL>"
                    },
                    "dateCreated": "2024-06-19",
                    "dateModified": "2024-07-05",
                    "keywords": ["design", "typography", "calligraphy", "generative art", "code tutorials"],
                    "inLanguage": ["en", "fa"]
                };

                const script = document.createElement('script');
                script.type = 'application/ld+json';
                script.textContent = JSON.stringify(portfolioData);
                document.head.appendChild(script);
            }

            // Initialize SEO enhancements
            addStructuredData();
            generateSitemapData();

            // --- Sidebar Menu Logic ---
            let sidebarExpanded = false;

            function toggleSidebar() {
                sidebarExpanded = !sidebarExpanded;

                if (sidebarExpanded) {
                    sidebarMenu.classList.add('expanded');
                    sidebarTrigger.classList.add('expanded');
                    rightLine.classList.add('expanded');
                } else {
                    sidebarMenu.classList.remove('expanded');
                    sidebarTrigger.classList.remove('expanded');
                    rightLine.classList.remove('expanded');
                }
            }

            function handleSectionClick(section) {
                // Remove active class from all sections
                document.querySelectorAll('.menu-section').forEach(s => s.classList.remove('active'));

                // Add active class to clicked section
                const clickedSection = document.querySelector(`[data-section="${section}"]`);
                if (clickedSection) {
                    clickedSection.classList.add('active');
                }

                // Handle section navigation
                switch(section) {
                    case 'home':
                        // Filter to show all items
                        filterItems('all');
                        break;
                    case 'projects':
                        // Could filter to specific project types
                        console.log('Projects section selected');
                        break;
                    case 'archive':
                        // Could show archived/older content
                        console.log('Archive section selected');
                        break;
                    case 'about':
                        // Could show about content
                        console.log('About section selected');
                        break;
                }

                // Close sidebar after selection
                setTimeout(() => {
                    toggleSidebar();
                }, 300);
            }

            // Sidebar event listeners
            sidebarTrigger.addEventListener('click', toggleSidebar);

            // Menu section event listeners
            document.querySelectorAll('.menu-section').forEach(section => {
                section.addEventListener('click', (e) => {
                    const sectionName = e.target.dataset.section;
                    handleSectionClick(sectionName);
                });
            });

            // Close sidebar when clicking outside
            document.addEventListener('click', (e) => {
                if (sidebarExpanded &&
                    !sidebarMenu.contains(e.target) &&
                    !sidebarTrigger.contains(e.target)) {
                    toggleSidebar();
                }
            });

            // --- Neat Grid Layout Algorithm ---
            function initializePerfectPacking() {
                // Sort tiles by date first (newest to oldest)
                sortTilesByDate();
                // Create a structured, even layout
                createNeatLayout();
            }

            function sortTilesByDate() {
                // Get all grid items and sort them by date
                const allTiles = Array.from(gridItems);

                allTiles.sort((a, b) => {
                    const dateA = new Date(a.dataset.date || '1970-01-01');
                    const dateB = new Date(b.dataset.date || '1970-01-01');
                    return dateB - dateA; // Newest first (descending order)
                });

                // Reorder tiles in the DOM
                const grid = document.getElementById('bento-grid');
                allTiles.forEach(tile => {
                    grid.appendChild(tile);
                });
            }

            function createNeatLayout() {
                const visibleTiles = gridItems.filter(item => item.dataset.hidden !== 'true');

                // Group tiles by type
                const largeTiles = visibleTiles.filter(tile => tile.classList.contains('tile-large'));
                const wideTiles = visibleTiles.filter(tile => tile.classList.contains('tile-wide'));
                const smallTiles = visibleTiles.filter(tile => tile.classList.contains('tile-small'));

                let currentColumn = 1;

                // Clear existing positioning classes
                visibleTiles.forEach(tile => {
                    tile.classList.remove(
                        'with-small-above', 'with-small-below', 'with-companion-above', 'with-companion-below',
                        'above-wide', 'below-wide', 'above-large', 'below-large'
                    );
                    tile.style.gridColumn = '';
                    tile.style.gridRow = '';
                });

                // Process tiles in order: large (with companions), wide (with small tiles), remaining tiles
                const processedTiles = new Set();

                // Place large tiles with companions (wide or small tiles)
                largeTiles.forEach((largeTile, largeIndex) => {
                    const availableWideTiles = wideTiles.filter(tile => !processedTiles.has(tile));
                    const availableSmallTiles = smallTiles.filter(tile => !processedTiles.has(tile));

                    // Try to pair with a wide tile first
                    if (availableWideTiles.length > 0) {
                        const companionWide = availableWideTiles[0];
                        const placeAbove = largeIndex % 2 === 0;

                        if (placeAbove) {
                            // Wide tile above, large tile below
                            companionWide.style.gridColumn = `${currentColumn} / span 2`;
                            companionWide.style.gridRow = `1 / span 1`;
                            companionWide.classList.add('above-large');

                            largeTile.style.gridColumn = `${currentColumn} / span 2`;
                            largeTile.style.gridRow = `2 / span 2`;
                            largeTile.classList.add('with-companion-above');
                        } else {
                            // Large tile above, wide tile below
                            largeTile.style.gridColumn = `${currentColumn} / span 2`;
                            largeTile.style.gridRow = `1 / span 2`;
                            largeTile.classList.add('with-companion-below');

                            companionWide.style.gridColumn = `${currentColumn} / span 2`;
                            companionWide.style.gridRow = `3 / span 1`;
                            companionWide.classList.add('below-large');
                        }

                        processedTiles.add(companionWide);
                    }
                    // Try to pair with 2 small tiles
                    else if (availableSmallTiles.length >= 2) {
                        const placeAbove = largeIndex % 2 === 0;

                        if (placeAbove) {
                            // Small tiles above, large tile below
                            availableSmallTiles[0].style.gridColumn = `${currentColumn} / span 1`;
                            availableSmallTiles[0].style.gridRow = `1 / span 1`;
                            availableSmallTiles[0].classList.add('above-large');

                            availableSmallTiles[1].style.gridColumn = `${currentColumn + 1} / span 1`;
                            availableSmallTiles[1].style.gridRow = `1 / span 1`;
                            availableSmallTiles[1].classList.add('above-large');

                            largeTile.style.gridColumn = `${currentColumn} / span 2`;
                            largeTile.style.gridRow = `2 / span 2`;
                            largeTile.classList.add('with-companion-above');
                        } else {
                            // Large tile above, small tiles below
                            largeTile.style.gridColumn = `${currentColumn} / span 2`;
                            largeTile.style.gridRow = `1 / span 2`;
                            largeTile.classList.add('with-companion-below');

                            availableSmallTiles[0].style.gridColumn = `${currentColumn} / span 1`;
                            availableSmallTiles[0].style.gridRow = `3 / span 1`;
                            availableSmallTiles[0].classList.add('below-large');

                            availableSmallTiles[1].style.gridColumn = `${currentColumn + 1} / span 1`;
                            availableSmallTiles[1].style.gridRow = `3 / span 1`;
                            availableSmallTiles[1].classList.add('below-large');
                        }

                        processedTiles.add(availableSmallTiles[0]);
                        processedTiles.add(availableSmallTiles[1]);
                    }
                    // Place large tile alone
                    else {
                        largeTile.style.gridColumn = `${currentColumn} / span 2`;
                        largeTile.style.gridRow = `1 / span 3`;
                    }

                    currentColumn += 2;
                    processedTiles.add(largeTile);
                });

                // Place remaining wide tiles with small tiles above/below
                const remainingWideTiles = wideTiles.filter(tile => !processedTiles.has(tile));
                remainingWideTiles.forEach((wideTile, wideIndex) => {
                    const availableSmallTiles = smallTiles.filter(tile => !processedTiles.has(tile));

                    if (availableSmallTiles.length >= 4) {
                        // Place wide tile in middle row with small tiles above and below
                        wideTile.style.gridColumn = `${currentColumn} / span 2`;
                        wideTile.style.gridRow = `2 / span 1`;
                        wideTile.classList.add('with-small-above', 'with-small-below');

                        // Place first small tile above
                        const smallAbove = availableSmallTiles[0];
                        smallAbove.style.gridColumn = `${currentColumn} / span 1`;
                        smallAbove.style.gridRow = `1 / span 1`;
                        smallAbove.classList.add('above-wide');
                        processedTiles.add(smallAbove);

                        // Place second small tile above (next column)
                        const smallAbove2 = availableSmallTiles[1];
                        smallAbove2.style.gridColumn = `${currentColumn + 1} / span 1`;
                        smallAbove2.style.gridRow = `1 / span 1`;
                        smallAbove2.classList.add('above-wide');
                        processedTiles.add(smallAbove2);

                        // Place small tiles below
                        const smallBelow = availableSmallTiles[2];
                        smallBelow.style.gridColumn = `${currentColumn} / span 1`;
                        smallBelow.style.gridRow = `3 / span 1`;
                        smallBelow.classList.add('below-wide');
                        processedTiles.add(smallBelow);

                        const smallBelow2 = availableSmallTiles[3];
                        smallBelow2.style.gridColumn = `${currentColumn + 1} / span 1`;
                        smallBelow2.style.gridRow = `3 / span 1`;
                        smallBelow2.classList.add('below-wide');
                        processedTiles.add(smallBelow2);

                    } else if (availableSmallTiles.length >= 2) {
                        // Place wide tile with small tiles only above or below (up to 4 tiles)
                        const placeAbove = wideIndex % 2 === 0;
                        const tilesToPlace = Math.min(4, availableSmallTiles.length);

                        if (placeAbove) {
                            wideTile.style.gridColumn = `${currentColumn} / span 2`;
                            wideTile.style.gridRow = `2 / span 1`;
                            wideTile.classList.add('with-small-above');

                            // Place small tiles above (up to 4, arranged in 2 rows if needed)
                            for (let i = 0; i < tilesToPlace; i++) {
                                const smallTile = availableSmallTiles[i];
                                const columnOffset = i % 2; // 0 or 1
                                const rowOffset = Math.floor(i / 2); // 0 for first 2 tiles, 1 for next 2

                                smallTile.style.gridColumn = `${currentColumn + columnOffset} / span 1`;
                                smallTile.style.gridRow = `${1 + rowOffset} / span 1`;
                                smallTile.classList.add('above-wide');
                                processedTiles.add(smallTile);
                            }

                            // Adjust wide tile position if we have more than 2 tiles above
                            if (tilesToPlace > 2) {
                                wideTile.style.gridRow = `3 / span 1`;
                            }
                        } else {
                            wideTile.style.gridColumn = `${currentColumn} / span 2`;
                            wideTile.style.gridRow = `1 / span 1`;
                            wideTile.classList.add('with-small-below');

                            // Place small tiles below (up to 4, arranged in 2 rows if needed)
                            for (let i = 0; i < tilesToPlace; i++) {
                                const smallTile = availableSmallTiles[i];
                                const columnOffset = i % 2; // 0 or 1
                                const rowOffset = Math.floor(i / 2); // 0 for first 2 tiles, 1 for next 2

                                smallTile.style.gridColumn = `${currentColumn + columnOffset} / span 1`;
                                smallTile.style.gridRow = `${2 + rowOffset} / span 1`;
                                smallTile.classList.add('below-wide');
                                processedTiles.add(smallTile);
                            }
                        }
                    } else {
                        // No small tiles available, place wide tile normally
                        wideTile.style.gridColumn = `${currentColumn} / span 2`;
                        wideTile.style.gridRow = `2 / span 1`;
                    }

                    currentColumn += 2;
                    processedTiles.add(wideTile);
                });

                // Place remaining small tiles in pairs
                const remainingSmallTiles = smallTiles.filter(tile => !processedTiles.has(tile));
                for (let i = 0; i < remainingSmallTiles.length; i += 2) {
                    const tile1 = remainingSmallTiles[i];
                    tile1.style.gridColumn = `${currentColumn} / span 1`;
                    tile1.style.gridRow = `1 / span 1`;

                    if (i + 1 < remainingSmallTiles.length) {
                        const tile2 = remainingSmallTiles[i + 1];
                        tile2.style.gridColumn = `${currentColumn} / span 1`;
                        tile2.style.gridRow = `2 / span 1`;
                    }

                    currentColumn += 1;
                }

                // Update grid width to accommodate all tiles
                const totalColumns = currentColumn - 1;
                grid.style.gridTemplateColumns = `repeat(${totalColumns}, 200px)`;
            }

            // Initialize perfect packing algorithm
            initializePerfectPacking();

            // Recalculate packing on window resize
            window.addEventListener('resize', () => {
                clearTimeout(window.resizeTimeout);
                window.resizeTimeout = setTimeout(() => {
                    initializePerfectPacking();
                }, 250);
            });

            // --- Scroll Speed Detection and Zoom Effect ---
            let scrollVelocity = 0;
            let lastScrollTime = Date.now();
            let lastScrollLeft = scrollContainer.scrollLeft;
            let scrollTimeout;
            let isScrolling = false;

            function updateScrollVelocity() {
                const currentTime = Date.now();
                const currentScrollLeft = scrollContainer.scrollLeft;
                const timeDelta = currentTime - lastScrollTime;
                const scrollDelta = Math.abs(currentScrollLeft - lastScrollLeft);

                if (timeDelta > 0) {
                    scrollVelocity = scrollDelta / timeDelta;
                }

                lastScrollTime = currentTime;
                lastScrollLeft = currentScrollLeft;

                // Apply zoom based on scroll velocity - DISABLED
                // applyScrollZoom();
            }

            function applyScrollZoom() {
                // ZOOM EFFECTS DISABLED - Function kept for compatibility
                return;

                // Calculate zoom level based on velocity (faster = more zoom out)
                const maxVelocity = 2; // Adjust this to control sensitivity
                const minZoom = 0.85; // Minimum zoom level (zoomed out)
                const maxZoom = 1; // Maximum zoom level (normal)

                // Clamp velocity and calculate zoom
                const clampedVelocity = Math.min(scrollVelocity, maxVelocity);
                const zoomLevel = maxZoom - (clampedVelocity / maxVelocity) * (maxZoom - minZoom);

                // Apply zoom to the grid
                gsap.to(grid, {
                    scale: zoomLevel,
                    duration: 0.3,
                    ease: "power2.out"
                });

                // Reset velocity gradually
                scrollVelocity *= 0.95;
            }

            function onScrollStart() {
                if (!isScrolling) {
                    isScrolling = true;
                }
            }

            function onScrollEnd() {
                isScrolling = false;
                scrollVelocity = 0;

                // ZOOM EFFECTS DISABLED - Grid stays at normal scale
                // gsap.to(grid, {
                //     scale: 1,
                //     duration: 0.6,
                //     ease: "power2.out"
                // });
            }

            // --- Horizontal Scroll with Mouse Wheel ---
            scrollContainer.addEventListener('wheel', (evt) => {
                if(isAnimating) {
                    evt.preventDefault();
                    return;
                }
                evt.preventDefault();

                onScrollStart();
                updateScrollVelocity();

                gsap.to(scrollContainer, {
                    scrollLeft: scrollContainer.scrollLeft + evt.deltaY,
                    duration: 0.5,
                    ease: "power2.out"
                });

                // Clear existing timeout and set new one
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(onScrollEnd, 150);
            });

            // Track scroll events for velocity calculation - DISABLED
            // scrollContainer.addEventListener('scroll', () => {
            //     if (!isScrolling) return;

            //     updateScrollVelocity();

            //     // Clear existing timeout and set new one
            //     clearTimeout(scrollTimeout);
            //     scrollTimeout = setTimeout(onScrollEnd, 150);
            // });

            // --- Initial Page Load Animation ---
            gsap.from(gridItems, {
                duration: 1,
                opacity: 0,
                scale: 0.5,
                x: 100,
                ease: "power3.out",
                stagger: { each: 0.08, from: "end" },
                onStart: () => {
                    gsap.set(gridItems, { visibility: 'visible' });
                },
                onComplete: () => {
                    // Update loading progress when content animation is complete
                    updateLoadingProgress(90);
                }
            });

            // --- Scroll-based Gradient ---
            function setupScrollAnimations() {
                ScrollTrigger.getAll().forEach(st => st.kill());

                ScrollTrigger.create({
                    trigger: scrollContainer,
                    scroller: scrollContainer,
                    horizontal: true,
                    start: "left left",
                    end: "right right",
                    onUpdate: self => {
                        // Update gradient
                        gsap.to(leftGradient, { opacity: self.progress > 0.01 ? 1 : 0, duration: 0.3 });
                    },
                });
            }

            setupScrollAnimations();

            // --- Filter & Admin Logic ---
            function applyFilterAndAnimate(filter) {
                const state = Flip.getState(gridItems, {props: "display"});

                gridItems.forEach(item => {
                    const categoryMatch = (filter === 'all' || item.dataset.category === filter);
                    const isVisibleByAdmin = item.dataset.visible === 'true';
                    item.dataset.hidden = !(categoryMatch && isVisibleByAdmin);
                });

                Flip.from(state, {
                    duration: 0.7,
                    scale: true,
                    ease: "power1.inOut",
                    stagger: 0.05,
                    absolute: true,
                    onEnter: elements => gsap.fromTo(elements, { opacity: 0, scale: 0.8 }, { opacity: 1, scale: 1, duration: 0.6 }),
                    onLeave: elements => gsap.to(elements, { opacity: 0, scale: 0.8, duration: 0.6 }),
                    onComplete: () => {
                        // Recalculate packing after filter animation
                        setTimeout(() => initializePerfectPacking(), 100);
                    }
                });
            }

            filterButtonsContainer.addEventListener('click', (e) => {
                const btn = e.target.closest('.filter-btn');
                if (!btn || isAnimating) return;

                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                applyFilterAndAnimate(btn.dataset.filter);
            });

            // --- Admin Mode ---
            document.addEventListener('keydown', (e) => {
                if (e.key === 'a') {
                    isAdminMode = !isAdminMode;
                    contentWrapper.classList.toggle('admin-mode', isAdminMode);
                    console.log(`Admin mode ${isAdminMode ? 'enabled' : 'disabled'}.`);
                }
            });

            grid.addEventListener('click', (e) => {
                const toggleBtn = e.target.closest('.admin-toggle-btn');
                if (isAdminMode && toggleBtn) {
                    e.stopPropagation();
                    const item = toggleBtn.closest('.grid-item');
                    const isVisible = item.dataset.visible === 'true';
                    item.dataset.visible = !isVisible;
                    
                    // Visually update icon
                    toggleBtn.querySelector('.visible-icon').style.display = !isVisible ? 'block' : 'none';
                    toggleBtn.querySelector('.hidden-icon').style.display = isVisible ? 'block' : 'none';

                    const currentFilter = filterButtonsContainer.querySelector('.active').dataset.filter;
                    applyFilterAndAnimate(currentFilter);
                }
            });
            
            // --- Fullscreen Logic ---
            function resetAllHoverStates() {
                gridItems.forEach(item => {
                    const title = item.querySelector('.layer-title');
                    const fg = item.querySelector('.layer-fg');
                    const mg = item.querySelector('.layer-mg');
                    const mg2 = item.querySelector('.layer-mg-2');
                    const bg = item.querySelector('.layer-bg');
                    const bg2 = item.querySelector('.layer-bg-2');

                    // Reset all hover transformations immediately
                    gsap.set(item, {
                        rotationX: 0,
                        rotationY: 0,
                        scale: 1,
                        zIndex: 1
                    });

                    // Reset all layers (filter out null values)
                    const layers = [title, fg, mg, mg2, bg, bg2].filter(layer => layer !== null);
                    gsap.set(layers, { x: 0, y: 0 });
                });
            }

            function openFullscreen(item) {
                if (isAnimating || isAdminMode) return;
                isAnimating = true;
                activeItem = item;

                // Reset all hover states when opening modal
                resetAllHoverStates();

                contentWrapper.classList.add('blurred');

                const detailContent = item.querySelector('.fullscreen-detail-content');
                fullscreenContent.innerHTML = detailContent ? detailContent.innerHTML : '<p>No details available.</p>';

                // Setup table of contents functionality
                setupTableOfContents();

                const state = Flip.getState(item);

                gsap.set(fullscreenModal, { visibility: 'visible', opacity: 1 });
                gsap.set(item, { visibility: 'hidden' });

                Flip.from(state, {
                    target: fullscreenModal,
                    duration: 0.8,
                    ease: "power2.inOut",
                    absolute: true,
                    onComplete: () => {
                        isAnimating = false;
                        gsap.to(fullscreenCloseBtn, { autoAlpha: 1, pointerEvents: 'auto', duration: 0.3 });
                    }
                });
            }

            function setupTableOfContents() {
                const tocItems = fullscreenContent.querySelectorAll('.toc-item');
                const sections = fullscreenContent.querySelectorAll('.fullscreen-section');
                const textContainer = fullscreenContent.querySelector('.fullscreen-text');

                if (!tocItems.length || !sections.length || !textContainer) return;

                tocItems.forEach(tocItem => {
                    tocItem.addEventListener('click', (e) => {
                        e.preventDefault();
                        const targetId = tocItem.getAttribute('data-target');
                        const targetSection = fullscreenContent.querySelector(`#${targetId}`);

                        if (targetSection) {
                            // Remove active class from all TOC items
                            tocItems.forEach(item => item.classList.remove('active'));
                            // Add active class to clicked item
                            tocItem.classList.add('active');

                            // Scroll to the target section
                            const sectionLeft = targetSection.offsetLeft - textContainer.offsetLeft;
                            textContainer.scrollTo({
                                left: sectionLeft,
                                behavior: 'smooth'
                            });
                        }
                    });
                });

                // Set first item as active by default
                if (tocItems.length > 0) {
                    tocItems[0].classList.add('active');
                }

                // Update active TOC item based on scroll position
                textContainer.addEventListener('scroll', () => {
                    let activeSection = null;
                    const scrollLeft = textContainer.scrollLeft;

                    sections.forEach(section => {
                        const sectionLeft = section.offsetLeft - textContainer.offsetLeft;
                        const sectionRight = sectionLeft + section.offsetWidth;

                        if (scrollLeft >= sectionLeft - 50 && scrollLeft < sectionRight - 50) {
                            activeSection = section;
                        }
                    });

                    if (activeSection) {
                        const targetId = activeSection.id;
                        tocItems.forEach(item => {
                            item.classList.remove('active');
                            if (item.getAttribute('data-target') === targetId) {
                                item.classList.add('active');
                            }
                        });
                    }
                });
            }

            function closeFullscreen() {
                if (isAnimating || !activeItem) return;
                isAnimating = true;
                
                contentWrapper.classList.remove('blurred');

                gsap.to(fullscreenCloseBtn, { autoAlpha: 0, pointerEvents: 'none', duration: 0.2 });
                
                gsap.to(fullscreenModal, {
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.in",
                    onComplete: () => {
                        gsap.set(fullscreenModal, { visibility: 'hidden' });
                        gsap.set(activeItem, { visibility: 'visible' });
                        fullscreenContent.innerHTML = '';
                        activeItem = null;
                        isAnimating = false;

                        // Reset all hover states when closing modal
                        resetAllHoverStates();

                        // Redraw the grid after modal closes
                        initializePerfectPacking();
                    }
                });
            }

            gridItems.forEach(item => {
                item.addEventListener('click', () => openFullscreen(item));
            });
            fullscreenCloseBtn.addEventListener('click', closeFullscreen);



            // -- smooth scrolling --

             // use a script tag or an external JS file
             document.addEventListener("scroll-container, fullscreen-section", (event) => {
             gsap.registerPlugin(ScrollTrigger,ScrollSmoother)
            ScrollSmoother.create({
                smooth: 1.5, // how long (in seconds) it takes to "catch up" to the native scroll position
                effects: true, // looks for data-speed and data-lag attributes on elements
                smoothTouch: 0.1, 
            });

            });

            // --- Input-based Interaction ---
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

            if (isTouchDevice) {
                // --- Gyroscope Logic ---
                permissionBtn.addEventListener('click', () => {
                    if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                        DeviceOrientationEvent.requestPermission()
                            .then(permissionState => {
                                if (permissionState === 'granted') {
                                    window.addEventListener('deviceorientation', handleOrientation);
                                    permissionModal.style.display = 'none';
                                }
                            })
                            .catch(console.error);
                    } else {
                        window.addEventListener('deviceorientation', handleOrientation);
                        permissionModal.style.display = 'none';
                    }
                });
                
                if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                    permissionModal.style.display = 'flex';
                } else {
                     window.addEventListener('deviceorientation', handleOrientation);
                }

                function handleOrientation(event) {
                    if(activeItem) return;
                    const beta = event.beta;
                    const gamma = event.gamma;
                    const maxTilt = 30;
                    const clampedBeta = gsap.utils.clamp(-maxTilt, maxTilt, beta);
                    const clampedGamma = gsap.utils.clamp(-maxTilt, maxTilt, gamma);
                    const percentY = clampedBeta / maxTilt;
                    const percentX = clampedGamma / maxTilt;
                    const maxRotation = 10;
                    
                    gridItems.forEach(item => {
                        if (item.dataset.hidden !== "true") {
                            const title = item.querySelector('.layer-title');
                            const fg = item.querySelector('.layer-fg');
                            const mg = item.querySelector('.layer-mg');
                            const mg2 = item.querySelector('.layer-mg-2');
                            const bg = item.querySelector('.layer-bg');
                            const bg2 = item.querySelector('.layer-bg-2');

                            gsap.to(item, { rotationX: percentY * -maxRotation, rotationY: percentX * maxRotation, ease: "power1.out", duration: 0.7 });

                            if (title) gsap.to(title, { x: percentX * 30, y: percentY * 30, ease: "power1.out", duration: 0.7 });
                            if (fg) gsap.to(fg, { x: percentX * 20, y: percentY * 20, ease: "power1.out", duration: 0.7 });
                            if (mg) gsap.to(mg, { x: percentX * 10, y: percentY * 10, ease: "power1.out", duration: 0.7 });
                            if (mg2) gsap.to(mg2, { x: percentX * 8, y: percentY * 8, ease: "power1.out", duration: 0.7 });
                            if (bg) gsap.to(bg, { x: percentX * 5, y: percentY * 5, ease: "power1.out", duration: 0.7 });
                            if (bg2) gsap.to(bg2, { x: percentX * 3, y: percentY * 3, ease: "power1.out", duration: 0.7 });
                        }
                    });
                }

            } else {
                // --- Mouse Logic ---
                gridItems.forEach(item => {
                    const title = item.querySelector('.layer-title');
                    const fg = item.querySelector('.layer-fg');
                    const mg = item.querySelector('.layer-mg');
                    const mg2 = item.querySelector('.layer-mg-2');
                    const bg = item.querySelector('.layer-bg');
                    const bg2 = item.querySelector('.layer-bg-2');
                    const maxRotation = 10;

                    item.addEventListener('mouseenter', (e) => {
                        if(activeItem) return;
                        gsap.to(item, { zIndex: 999, duration: 0.1 });
                    });

                    item.addEventListener('mousemove', (e) => {
                        if(activeItem) return;
                        const rect = item.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;
                        const centerX = rect.width / 2;
                        const centerY = rect.height / 2;
                        const percentX = (x - centerX) / centerX;
                        const percentY = (y - centerY) / centerY;

                        gsap.to(item, { rotationX: percentY * -maxRotation, rotationY: percentX * maxRotation, scale: 1, ease: "power1.out", duration: 0.7 });

                        if (title) gsap.to(title, { x: percentX * 30, y: percentY * 30, ease: "power1.out", duration: 0.7 });
                        if (fg) gsap.to(fg, { x: percentX * 20, y: percentY * 20, ease: "power1.out", duration: 0.7 });
                        if (mg) gsap.to(mg, { x: percentX * 10, y: percentY * 10, ease: "power1.out", duration: 0.7 });
                        if (mg2) gsap.to(mg2, { x: percentX * 8, y: percentY * 8, ease: "power1.out", duration: 0.7 });
                        if (bg) gsap.to(bg, { x: percentX * 5, y: percentY * 5, ease: "power1.out", duration: 0.7 });
                        if (bg2) gsap.to(bg2, { x: percentX * 3, y: percentY * 3, ease: "power1.out", duration: 0.7 });
                    });

                    item.addEventListener('mouseleave', (e) => {
                        if(activeItem) return;
                        const tl = gsap.timeline({ defaults: { ease: "power3.out", duration: 1, overwrite: 'auto' } });
                        tl.to(item, { rotationX: 0, rotationY: 0, scale: 1, zIndex: 1 }, 0);

                        // Reset all layers (filter out null values)
                        const layers = [title, fg, mg, mg2, bg, bg2].filter(layer => layer !== null);
                        tl.to(layers, { x: 0, y: 0 }, 0);
                    });
                });
            }
        });
    </script>

</body>
</html>
