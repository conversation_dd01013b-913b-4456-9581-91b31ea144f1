import GSAP from 'gsap';
import ScrollTrigger from 'gsap/ScrollTrigger';
import Lenis from 'lenis'

console.log ("hello There");

// Smooth Scrolling - Adjusted for simpler scrolling
const lenis = new Lenis({
    orientation: 'horizontal',
    smoothWheel: false, // Disabled smooth wheel for more direct scrolling
    gestureOrientation: 'both',
    direction: 'horizontal',
    duration: 0.8, // Reduced duration for more responsive scrolling
    smoothTouch: false, // Disabled smooth touch for more direct scrolling
    touchMultiplier: 2, // Reduced multiplier
    autoResize: true,
});
lenis.on('scroll', ScrollTrigger.update, 'direction','progress' ) 
console.log(lenis)

GSAP.ticker.add((time) => {
    lenis.raf(time * 1000)
})

GSAP.ticker.lagSmoothing(0)

function raf(time) {
    lenis.raf(time)
    requestAnimationFrame(raf)
  }
  
  requestAnimationFrame(raf)

  // -----

  const slider = document.querySelector('.slider');
  const section = document.querySelectorAll('.slider section');

  console.log(section);